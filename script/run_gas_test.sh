#!/bin/bash

# Gas Test Script for DistributeRewards
# This script runs the gas consumption test for the distributeRewards method

echo "=== DistributeRewards Gas Test ==="
echo "This script will test the gas consumption of distributeRewards method with 10 recipients"
echo ""

# Check if RPC URL is provided
if [ -z "$1" ]; then
    echo "Usage: $0 <RPC_URL>"
    echo "Example: $0 https://your-mainnet-rpc-url"
    exit 1
fi

RPC_URL=$1
echo "Using RPC URL: $RPC_URL"
echo ""

# Set proxy if needed (based on user's memory)
export https_proxy=http://127.0.0.1:7897
export http_proxy=http://127.0.0.1:7897
export all_proxy=socks5://127.0.0.1:7897

echo "Proxy configured for testing"
echo ""

# Run the forge script
echo "Running gas test..."
forge script script/GasTestDistributeRewards.s.sol:GasTestDistributeRewards \
    --rpc-url "$RPC_URL" \
    --broadcast \
    --gas-estimate-multiplier 200 \
    -vvv

echo ""
echo "Gas test completed!"
