# DistributeRewards Gas Test Commands
#
# This file contains the commands to run the gas consumption test for the distributeRewards method
# Replace YOUR_RPC_URL with your actual mainnet RPC URL

# Method 1: Using forge script with fork (Recommended)
# Set proxy first (based on user's testing environment)
export https_proxy=http://127.0.0.1:7897 http_proxy=http://127.0.0.1:7897 all_proxy=socks5://127.0.0.1:7897

# Run the gas test script
forge script script/GasTestDistributeRewards.s.sol:GasTestDistributeRewards \
    --fork-url YOUR_RPC_URL \
    --broadcast \
    --gas-estimate-multiplier 200 \
    -vvv

# Method 2: Alternative with different verbosity
forge script script/GasTestDistributeRewards.s.sol:GasTestDistributeRewards \
    --fork-url YOUR_RPC_URL \
    --broadcast \
    --gas-estimate-multiplier 200 \
    --slow \
    -vv

# Method 3: Simulation only (no broadcast)
forge script script/GasTestDistributeRewards.s.sol:GasTestDistributeRewards \
    --fork-url YOUR_RPC_URL \
    --gas-estimate-multiplier 200 \
    -vvv
