// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.20;

import "forge-std/Script.sol";
import "forge-std/console.sol";
import "../src/DistributionPool.sol";
import "../src/Factory.sol";
import "../src/IPCoin.sol";
import "../src/CreatorNft.sol";
import "@openzeppelin/contracts/proxy/ERC1967/ERC1967Proxy.sol";

/**
 * @title Gas Test for DistributeRewards
 * @notice Script to test gas consumption of distributeRewards method with 10 recipients
 */
contract GasTestDistributeRewards is Script {
    DistributionPool public pool;
    Factory public factory;
    IPCoin public coin;
    CreatorNft public creatorNft;

    address public owner;
    address public feeReceiver;
    address public creator;
    address public aiWallet;
    address public distributor;

    // Test recipients
    address[10] public recipients;

    function setUp() public {
        // Set up test addresses
        owner = address(0x1000);
        feeReceiver = address(0x2000);
        creator = address(0x3000);
        aiWallet = address(0x4000);
        distributor = address(0x5000);

        // Set up 10 recipient addresses
        for (uint256 i = 0; i < 10; i++) {
            recipients[i] = address(uint160(0x6000 + i));
        }

        console.log("=== Gas Test Setup ===");
        console.log("Owner:", owner);
        console.log("Fee Receiver:", feeReceiver);
        console.log("Creator:", creator);
        console.log("AI Wallet:", aiWallet);
        console.log("Distributor:", distributor);
    }

    function run() public {
        vm.startBroadcast();

        setUp();

        // Step 1: Deploy contracts
        deployContracts();

        // Step 2: Create IP group
        createIPGroup();

        // Step 3: Configure permissions and initial state
        configureTestEnvironment();

        // Step 4: Test gas consumption
        testDistributeRewardsGas();

        vm.stopBroadcast();
    }

    function deployContracts() internal {
        console.log("\n=== Deploying Contracts ===");

        // Deploy DistributionPool implementation and proxy
        DistributionPool poolImpl = new DistributionPool();
        address poolProxy = address(new ERC1967Proxy(address(poolImpl), ""));
        pool = DistributionPool(payable(poolProxy));
        console.log("DistributionPool deployed at:", address(pool));

        // Deploy CreatorNft implementation
        CreatorNft creatorNftImpl = new CreatorNft();
        console.log("CreatorNft implementation deployed at:", address(creatorNftImpl));

        // Deploy Factory implementation and proxy
        Factory factoryImpl = new Factory();
        address factoryProxy = address(new ERC1967Proxy(address(factoryImpl), ""));
        factory = Factory(factoryProxy);
        console.log("Factory deployed at:", address(factory));

        // Initialize Factory
        factory.initialize(owner, address(creatorNftImpl), address(pool));
        console.log("Factory initialized");

        // Initialize DistributionPool
        pool.initialize(owner, feeReceiver, address(factory));
        console.log("DistributionPool initialized");
    }

    function createIPGroup() internal {
        console.log("\n=== Creating IP Group ===");

        // Prepare deployment parameters
        Factory.DeploymentWithOutTraderNftParams memory params = Factory.DeploymentWithOutTraderNftParams({
            coinName: "Test IP Coin",
            coinSymbol: "TIC",
            creator: creator,
            aiAgentWallet: aiWallet,
            creatorNftName: "Test Creator NFT",
            creatorNftSymbol: "TCN",
            creatorNftURI: "https://test.com/creator/1",
            ipRef: "testIPRef"
        });

        // Deploy IP group through Factory
        address coinAddress = factory.deployIPWithoutTraderNft(params);
        coin = IPCoin(coinAddress);
        console.log("IPCoin deployed at:", coinAddress);

        // Get CreatorNft address from the coin pool data
        // CoinPoolData has 19 fields: ipCoinContract, traderNftContract, creatorNftContract, allowTrade, tradeNftStatus,
        // pairAddress, aiWallet, poolVersion, tokenAmountInPool, nativeAmountInPool, intervalRewardDistributionTotal,
        // epochCreatorRewardDistributionTotal, lastDistributionTimestamp, lastCreatorDistributionTimestamp,
        // cumulativeDistribution, remainingSupply, creationTimestamp, specificRewardInterval, parameters
        (,, CreatorNft creatorNftContract,,,,,,,,,,,,,,,,) = pool.coinPoolsData(coinAddress);
        creatorNft = creatorNftContract;
        console.log("CreatorNft deployed at:", address(creatorNft));
    }

    function configureTestEnvironment() internal {
        console.log("\n=== Configuring Test Environment ===");

        // Add distributor to whitelist
        vm.prank(owner);
        pool.addRewardDistributor(distributor);
        console.log("Added distributor to whitelist");

        // Set a short reward interval for testing (1 second)
        vm.prank(owner);
        pool.setRewardInterval(address(coin), 1);
        console.log("Set reward interval to 1 second");

        // Distribute creator rewards to initialize state and provide tokens
        uint256 initialDistribution = 10_000 * 1e18; // 10,000 tokens
        vm.prank(distributor);
        pool.distributeCreatorRewards(address(coin), initialDistribution);
        console.log("Initial creator rewards distributed:", initialDistribution);

        // Check AI wallet balance
        uint256 aiWalletBalance = coin.balanceOf(aiWallet);
        console.log("AI Wallet balance after creator distribution:", aiWalletBalance);

        // Transfer some tokens from AI wallet to distributor for testing
        uint256 transferAmount = 1000 * 1e18; // 1,000 tokens for distribution
        vm.prank(aiWallet);
        coin.transfer(distributor, transferAmount);
        console.log("Transferred tokens to distributor:", transferAmount);

        uint256 distributorBalance = coin.balanceOf(distributor);
        console.log("Distributor balance:", distributorBalance);
    }

    function testDistributeRewardsGas() internal {
        console.log("\n=== Testing DistributeRewards Gas Consumption ===");

        // Prepare reward distributions for 10 recipients
        DistributionPool.RewardDistribution[] memory distributions = new DistributionPool.RewardDistribution[](10);

        uint256 rewardPerRecipient = 10 * 1e18; // 10 tokens per recipient
        uint256 totalReward = rewardPerRecipient * 10; // 100 tokens total

        for (uint256 i = 0; i < 10; i++) {
            distributions[i] =
                DistributionPool.RewardDistribution({recipient: recipients[i], amount: rewardPerRecipient});
        }

        console.log("Prepared distributions for 10 recipients");
        console.log("Reward per recipient:", rewardPerRecipient);
        console.log("Total reward amount:", totalReward);

        // Check distributor has enough balance
        uint256 distributorBalance = coin.balanceOf(distributor);
        require(distributorBalance >= totalReward, "Insufficient distributor balance");

        // Approve the pool to spend distributor's tokens
        vm.prank(distributor);
        coin.approve(address(pool), totalReward);
        console.log("Approved pool to spend tokens");

        // Wait a bit to ensure time interval requirement is met
        vm.warp(block.timestamp + 2);

        // Record gas before the call
        uint256 gasStart = gasleft();

        // Call distributeRewards and measure gas
        vm.prank(distributor);
        pool.distributeRewards(address(coin), distributions);

        uint256 gasEnd = gasleft();
        uint256 gasUsed = gasStart - gasEnd;

        console.log("\n=== Gas Consumption Results ===");
        console.log("Total gas used for distributeRewards:", gasUsed);
        console.log("Average gas per recipient:", gasUsed / 10);
        console.log("Gas per token distributed:", gasUsed / totalReward);

        // Verify distributions were successful
        for (uint256 i = 0; i < 10; i++) {
            uint256 recipientBalance = coin.balanceOf(recipients[i]);
            require(recipientBalance == rewardPerRecipient, "Distribution failed");
        }

        console.log("All distributions verified successfully");

        // Additional gas analysis with different batch sizes
        console.log("\n=== Additional Gas Analysis ===");
        performBatchSizeAnalysis();
    }

    function performBatchSizeAnalysis() internal {
        console.log("Performing batch size analysis...");

        // Test with different batch sizes: 1, 5, 10 recipients
        uint256[] memory batchSizes = new uint256[](3);
        batchSizes[0] = 1;
        batchSizes[1] = 5;
        batchSizes[2] = 10;

        for (uint256 j = 0; j < batchSizes.length; j++) {
            uint256 batchSize = batchSizes[j];

            // Prepare distributions
            DistributionPool.RewardDistribution[] memory batchDistributions =
                new DistributionPool.RewardDistribution[](batchSize);

            uint256 rewardPerRecipient = 5 * 1e18; // 5 tokens per recipient
            uint256 totalBatchReward = rewardPerRecipient * batchSize;

            for (uint256 i = 0; i < batchSize; i++) {
                // Use different recipients to avoid interval restrictions
                address recipient = address(uint160(0x7000 + j * 100 + i));
                batchDistributions[i] =
                    DistributionPool.RewardDistribution({recipient: recipient, amount: rewardPerRecipient});
            }

            // Approve tokens
            vm.prank(distributor);
            coin.approve(address(pool), totalBatchReward);

            // Wait to meet interval requirement
            vm.warp(block.timestamp + 2);

            // Measure gas
            uint256 gasStart = gasleft();
            vm.prank(distributor);
            pool.distributeRewards(address(coin), batchDistributions);
            uint256 gasEnd = gasleft();
            uint256 gasUsed = gasStart - gasEnd;

            console.log("Batch size:", batchSize);
            console.log("  Total gas used:", gasUsed);
            console.log("  Gas per recipient:", gasUsed / batchSize);
            console.log("  Gas efficiency vs single:", gasUsed * 100 / (gasUsed / batchSize));
        }
    }
}
