// SPDX-License-Identifier: MIT
pragma solidity ^0.8.26;

import "forge-std/Script.sol";
import "forge-std/console.sol";
import "../src/DistributionPool.sol";
import "../src/Factory.sol";
import "../src/IPCoin.sol";

contract MinimalGasTest is Script {
    DistributionPool pool;
    IPCoin coin;
    address owner = address(this);
    address aiWallet = address(0x4);
    address user1 = address(0x1);

    function setUp() public {
        console.log("Starting minimal gas test setup");

        // Deploy DistributionPool implementation
        DistributionPool poolImpl = new DistributionPool();

        // Deploy proxy
        bytes memory initData = abi.encodeWithSelector(
            DistributionPool.initialize.selector,
            owner,
            address(0x3), // treasury
            address(0x5) // factory (placeholder)
        );

        ERC1967Proxy poolProxy = new ERC1967Proxy(address(poolImpl), initData);
        pool = DistributionPool(payable(address(poolProxy)));

        // Deploy a simple IPCoin for testing (pool is the minter)
        // Don't mint here - let addIp do the minting
        coin = new IPCoin("Test Coin", "TEST", address(pool));

        console.log("Setup completed");
    }

    function run() public {
        setUp();

        console.log("=== Minimal Gas Test for DistributeRewards ===");
        console.log("Testing gas consumption for 10 recipients");

        // Use setFactory to grant ADDIP_ROLE to owner
        vm.prank(owner);
        pool.setFactory(owner); // This will grant ADDIP_ROLE to owner
        console.log("Granted ADDIP_ROLE to owner via setFactory");

        // Add coin to the pool (this will mint 1B tokens to the pool)
        vm.prank(owner);
        pool.addIp(address(coin), address(0), aiWallet); // No creator NFT for this test
        console.log("Added coin to pool");

        // Transfer some tokens to aiWallet for testing
        vm.prank(address(pool));
        coin.transfer(aiWallet, 1000 * 1e18);
        console.log("Transferred tokens to aiWallet");

        // Add user1 as distributor
        vm.prank(owner);
        pool.addRewardDistributor(user1);
        console.log("Added user1 to distributor whitelist");

        // Prepare test data for 10 recipients with small amounts (1% of wallet)
        uint256 rewardPerRecipient = 1 * 1e18; // 1 token per recipient
        uint256 totalReward = rewardPerRecipient * 10; // 10 tokens total (1% of 1000)

        // Prepare 10 recipients
        address[10] memory recipients;
        for (uint256 i = 0; i < 10; i++) {
            recipients[i] = address(uint160(0x6000 + i));
        }

        // Prepare reward distributions
        DistributionPool.RewardDistribution[] memory distributions = new DistributionPool.RewardDistribution[](10);

        for (uint256 i = 0; i < 10; i++) {
            distributions[i] =
                DistributionPool.RewardDistribution({recipient: recipients[i], amount: rewardPerRecipient});
        }

        console.log("Prepared distributions for 10 recipients");
        console.log("Reward per recipient:", rewardPerRecipient);
        console.log("Total reward amount:", totalReward);

        // Check aiWallet has enough balance
        uint256 aiWalletBalance = coin.balanceOf(aiWallet);
        console.log("AI Wallet balance:", aiWalletBalance);

        // Approve pool to spend tokens
        vm.prank(aiWallet);
        coin.approve(address(pool), totalReward);
        console.log("Approved pool to spend tokens");

        // Advance time to ensure reward interval is met
        vm.warp(block.timestamp + 1 days);

        console.log("\n=== Gas Measurement ===");

        // Measure gas for distributeRewards
        uint256 gasBefore = gasleft();

        vm.prank(aiWallet);
        pool.distributeRewards(address(coin), distributions);

        uint256 gasUsed = gasBefore - gasleft();

        console.log("Gas used for distributeRewards (10 recipients):", gasUsed);
        console.log("Average gas per recipient:", gasUsed / 10);

        // Verify distributions were successful
        for (uint256 i = 0; i < 10; i++) {
            uint256 balance = coin.balanceOf(recipients[i]);
            require(balance == rewardPerRecipient, "Distribution failed");
        }

        console.log("All distributions verified successfully");
        console.log("=== Test Completed ===");
    }
}
