// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.20;

import "forge-std/Script.sol";
import "forge-std/console.sol";
import "../test/DistributionPool.t.sol";

/**
 * @title Simple Gas Test for DistributeRewards
 * @notice Simplified script to test gas consumption of distributeRewards method
 */
contract SimpleGasTest is DistributionPoolTest {
    function run() public {
        // Use the existing test setup
        setUp();

        console.log("=== Simple Gas Test for DistributeRewards ===");
        console.log("Testing gas consumption for 10 recipients");

        // Add user1 to whitelist (user1 will be our distributor)
        vm.prank(owner);
        pool.addRewardDistributor(user1);
        console.log("Added user1 to distributor whitelist");

        // Set a short reward interval for testing
        vm.prank(owner);
        pool.setRewardInterval(address(coin), 1);
        console.log("Set reward interval to 1 second");

        // Note: Default max wallet distribution percentage is 10% (1000 basis points)
        // We'll keep the distribution amount small to stay within this limit

        // Distribute creator rewards to initialize state and provide tokens
        uint256 initialDistribution = 10_000 * 1e18;
        vm.prank(user1);
        pool.distributeCreatorRewards(address(coin), initialDistribution);
        console.log("Initial creator rewards distributed:", initialDistribution);

        // Transfer some tokens from AI wallet to user1 for testing
        uint256 transferAmount = 1000 * 1e18;
        vm.prank(aiWallet);
        coin.transfer(user1, transferAmount);
        console.log("Transferred tokens to user1:", transferAmount);

        // Prepare 10 recipients
        address[10] memory recipients;
        for (uint256 i = 0; i < 10; i++) {
            recipients[i] = address(uint160(0x6000 + i));
        }

        // Prepare reward distributions
        DistributionPool.RewardDistribution[] memory distributions = new DistributionPool.RewardDistribution[](10);

        // Keep total under 10% of AI wallet balance (1000 tokens * 10% = 100 tokens)
        // Use 5% to be safe: 50 tokens total
        uint256 rewardPerRecipient = 5 * 1e18; // 5 tokens per recipient
        uint256 totalReward = rewardPerRecipient * 10; // 50 tokens total

        for (uint256 i = 0; i < 10; i++) {
            distributions[i] =
                DistributionPool.RewardDistribution({recipient: recipients[i], amount: rewardPerRecipient});
        }

        console.log("Prepared distributions for 10 recipients");
        console.log("Reward per recipient:", rewardPerRecipient);
        console.log("Total reward amount:", totalReward);

        // Check aiWallet has enough balance (it should have received tokens from creator distribution)
        uint256 aiWalletBalance = coin.balanceOf(aiWallet);
        require(aiWalletBalance >= totalReward, "Insufficient aiWallet balance");
        console.log("AI Wallet balance:", aiWalletBalance);

        // aiWallet needs to approve the pool to spend its tokens
        vm.prank(aiWallet);
        coin.approve(address(pool), totalReward);
        console.log("Approved pool to spend tokens");

        // Wait to ensure time interval requirement is met
        vm.warp(block.timestamp + 2);

        // Measure gas consumption
        console.log("\n=== Gas Measurement ===");

        uint256 gasStart = gasleft();

        // Call distributeRewards and measure gas (must be called by aiWallet)
        vm.prank(aiWallet);
        pool.distributeRewards(address(coin), distributions);

        uint256 gasEnd = gasleft();
        uint256 gasUsed = gasStart - gasEnd;

        console.log("=== Gas Consumption Results ===");
        console.log("Total gas used for distributeRewards:", gasUsed);
        console.log("Average gas per recipient:", gasUsed / 10);
        console.log("Gas per token distributed (wei):", gasUsed / totalReward);

        // Verify distributions were successful
        for (uint256 i = 0; i < 10; i++) {
            uint256 recipientBalance = coin.balanceOf(recipients[i]);
            require(recipientBalance == rewardPerRecipient, "Distribution failed");
        }

        console.log("All distributions verified successfully");

        // Test different batch sizes for comparison
        testBatchSizes();
    }

    function testBatchSizes() internal {
        console.log("\n=== Batch Size Analysis ===");

        uint256[] memory batchSizes = new uint256[](3);
        batchSizes[0] = 1;
        batchSizes[1] = 5;
        batchSizes[2] = 10;

        for (uint256 j = 0; j < batchSizes.length; j++) {
            uint256 batchSize = batchSizes[j];

            // Prepare distributions
            DistributionPool.RewardDistribution[] memory batchDistributions =
                new DistributionPool.RewardDistribution[](batchSize);

            uint256 rewardPerRecipient = 5 * 1e18; // 5 tokens per recipient
            uint256 totalBatchReward = rewardPerRecipient * batchSize;

            for (uint256 i = 0; i < batchSize; i++) {
                // Use different recipients to avoid interval restrictions
                address recipient = address(uint160(0x7000 + j * 100 + i));
                batchDistributions[i] =
                    DistributionPool.RewardDistribution({recipient: recipient, amount: rewardPerRecipient});
            }

            // Approve tokens (aiWallet needs to approve)
            vm.prank(aiWallet);
            coin.approve(address(pool), totalBatchReward);

            // Wait to meet interval requirement
            vm.warp(block.timestamp + 2);

            // Measure gas
            uint256 gasStart = gasleft();
            vm.prank(aiWallet);
            pool.distributeRewards(address(coin), batchDistributions);
            uint256 gasEnd = gasleft();
            uint256 gasUsed = gasStart - gasEnd;

            console.log("Batch size:", batchSize);
            console.log("  Total gas used:", gasUsed);
            console.log("  Gas per recipient:", gasUsed / batchSize);
        }
    }
}
